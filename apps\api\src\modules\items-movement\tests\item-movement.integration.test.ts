import request from 'supertest';
import path from 'path';
import fs from 'fs';
import {
  app,
  setupTestFolders,
  cleanupDatabase,
  setupTestData,
  cleanupFiles,
} from '../../../core/utils/test/integration-setup.js';
import { config } from '../../../core/config/app.config.js';
import { prisma } from '../../../core/utils/prisma.js';

/**
 * اختبارات تكامل وحدة حركة الأصناف
 */
describe('Item Movements Integration Tests', () => {
  let authToken: string;
  let userId: string;
  let clientId: string;
  let declarationId: string;
  let createdFiles: string[] = [];
  let itemMovementId: string;

  // قبل جميع الاختبارات
  beforeAll(async () => {
    // إعداد بيئة الاختبار
    setupTestFolders();
  });

  // قبل كل اختبار
  beforeEach(async () => {
    // تنظيف قاعدة البيانات
    await cleanupDatabase();

    // إنشاء بيانات الاختبار
    const { user, authToken: token, client } = await setupTestData();
    userId = user.id;
    authToken = token;
    clientId = client.id;

    // إنشاء بيان للاختبار مع التأكد من صحة البيانات
    const timestamp = Date.now();
    const declaration = await prisma.declaration.create({
      data: {
        declarationNumber: `3001-${timestamp}`,
        taxNumber: '*********',
        clientName: 'عميل اختبار',
        companyName: 'شركة اختبار',
        policyNumber: '7001',
        invoiceNumber: '12001',
        gatewayEntryNumber: '4001',
        declarationType: 'IMPORT',
        declarationDate: new Date(),
        count: 100,
        weight: 500.0,
        goodsType: 'MEDICAL_SUPPLIES',
        itemsCount: 5,
        entryDate: new Date(),
        exitDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
        clientId: clientId,
        userId: userId, // إضافة userId للبيان
      },
    });

    declarationId = declaration.id;

    // التحقق من أن declarationId هو UUID صالح
    console.log('🔍 Declaration ID created:', declarationId);
    expect(declarationId).toBeDefined();
    expect(typeof declarationId).toBe('string');
    expect(declarationId.length).toBeGreaterThan(0);
  });

  // بعد جميع الاختبارات
  afterAll(async () => {
    // تنظيف الملفات المؤقتة
    cleanupFiles(createdFiles);
  });

  /**
   * اختبار إنشاء حركة صنف جديدة
   */
  describe('POST /api/item-movements', () => {
    it('يجب أن ينشئ حركة صنف جديدة بنجاح', async () => {
      const response = await request(app)
        .post('/api/item-movements')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          itemName: 'هاتف ذكي',
          quantity: 10,
          unit: 'قطعة',
          movementType: 'IN',
          movementDate: new Date().toISOString(),
          declarationId: declarationId,
          notes: 'اختبار حركة صنف'
        })
        .expect(201);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toBeDefined();
      expect(response.body.data.itemName).toBe('هاتف ذكي');
      expect(response.body.data.quantity).toBe(10);
      expect(response.body.data.declarationId).toBe(declarationId);

      // حفظ معرف حركة الصنف للاختبارات اللاحقة
      itemMovementId = response.body.data.id;
    });

    it('يجب أن يرفض إنشاء حركة صنف بدون بيانات مطلوبة', async () => {
      const response = await request(app)
        .post('/api/item-movements')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          itemName: 'هاتف ذكي',
          // عدم إرسال quantity (مطلوبة)
          unit: 'قطعة',
          movementType: 'IN',
          movementDate: new Date().toISOString(),
          declarationId: declarationId
        })
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toBeDefined();
    });
  });

  /**
   * اختبار الحصول على حركات الأصناف
   */
  describe('GET /api/item-movements', () => {
    beforeEach(async () => {
      // إنشاء حركة صنف للاختبار
      await request(app)
        .post('/api/item-movements')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          itemName: 'هاتف ذكي',
          quantity: 10,
          unit: 'قطعة',
          movementType: 'IN',
          movementDate: new Date().toISOString(),
          declarationId: declarationId
        });
    });

    it('يجب أن يعيد قائمة حركات الأصناف بنجاح', async () => {
      const response = await request(app)
        .get('/api/item-movements')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toBeDefined();
      expect(Array.isArray(response.body.data)).toBe(true);
      expect(response.body.data.length).toBeGreaterThan(0);
      expect(response.body.pagination).toBeDefined();
    });

    it('يجب أن يعيد حركات الأصناف مع تصفية حسب البيان', async () => {
      const response = await request(app)
        .get('/api/item-movements')
        .set('Authorization', `Bearer ${authToken}`)
        .query({ declarationId: declarationId })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toBeDefined();
      expect(Array.isArray(response.body.data)).toBe(true);
      expect(response.body.data.length).toBeGreaterThan(0);
      expect(response.body.data[0].declarationId).toBe(declarationId);
    });
  });

  /**
   * اختبار الحصول على حركة صنف محددة
   */
  describe('GET /api/item-movements/:id', () => {
    beforeEach(async () => {
      // إنشاء حركة صنف للاختبار
      const response = await request(app)
        .post('/api/item-movements')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          itemName: 'هاتف ذكي',
          quantity: 10,
          unit: 'قطعة',
          movementType: 'IN',
          movementDate: new Date().toISOString(),
          declarationId: declarationId
        });

      if (response.body.data) {
        itemMovementId = response.body.data.id;
      }
    });

    it('يجب أن يعيد حركة صنف محددة بنجاح', async () => {
      const response = await request(app)
        .get(`/api/item-movements/${itemMovementId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toBeDefined();
      expect(response.body.data.id).toBe(itemMovementId);
      expect(response.body.data.itemName).toBe('هاتف ذكي');
      expect(response.body.data.quantity).toBe(10);

    });

    it('يجب أن يعيد خطأ عند طلب حركة صنف غير موجودة', async () => {
      const response = await request(app)
        .get('/api/item-movements/non-existent-id')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(404);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toBeDefined();
    });
  });

  /**
   * اختبار تحديث حركة صنف
   */
  describe('PUT /api/item-movements/:id', () => {
    beforeEach(async () => {
      // إنشاء حركة صنف للاختبار
      const response = await request(app)
        .post('/api/item-movements')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          itemName: 'هاتف ذكي',
          quantity: 10,
          unit: 'قطعة',
          movementType: 'IN',
          movementDate: new Date().toISOString(),
          declarationId: declarationId
        });

      if (response.body.data) {
        itemMovementId = response.body.data.id;
      }
    });

    it('يجب أن يحدث حركة صنف بنجاح', async () => {
      const response = await request(app)
        .put(`/api/item-movements/${itemMovementId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          itemName: 'هاتف ذكي محدث',
          quantity: 15,
          unit: 'قطعة',
          movementType: 'OUT',
          movementDate: new Date().toISOString()
        })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toBeDefined();
      expect(response.body.data.id).toBe(itemMovementId);
      expect(response.body.data.itemName).toBe('هاتف ذكي محدث');
      expect(response.body.data.quantity).toBe(15);
    });
  });

  /**
   * اختبار حذف حركة صنف
   */
  describe('DELETE /api/item-movements/:id', () => {
    beforeEach(async () => {
      // إنشاء حركة صنف للاختبار
      const response = await request(app)
        .post('/api/item-movements')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          itemName: 'هاتف ذكي',
          quantity: 10,
          unit: 'قطعة',
          movementType: 'IN',
          movementDate: new Date().toISOString(),
          declarationId: declarationId
        });

      if (response.body.data) {
        itemMovementId = response.body.data.id;
      }
    });

    it('يجب أن يحذف حركة صنف بنجاح', async () => {
      const response = await request(app)
        .delete(`/api/item-movements/${itemMovementId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toBeDefined();
      expect(response.body.data.id).toBe(itemMovementId);

      // التحقق من حذف حركة الصنف من قاعدة البيانات
      const deletedItemMovement = await prisma.itemMovement.findUnique({
        where: { id: itemMovementId },
      });
      expect(deletedItemMovement).toBeNull();
    });
  });
});
