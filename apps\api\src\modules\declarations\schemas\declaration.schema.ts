import { z } from 'zod';

// مخطط إنشاء البيان
export const createDeclarationSchema = z.object({
  body: z.object({
    taxNumber: z.string({
      required_error: 'الرقم الضريبي مطلوب',
    }),
    clientName: z.string().optional(),
    companyName: z.string().optional(),
    policyNumber: z.union([z.number(), z.string().transform(val => val ? parseFloat(val) : undefined)]).optional(),
    invoiceNumber: z.union([z.number(), z.string().transform(val => val ? parseFloat(val) : undefined)]).optional(),
    gatewayEntryNumber: z.union([
      z.number(),
      z.string().transform(val => {
        const num = parseFloat(val);
        if (isNaN(num)) {
          throw new Error('رقم دخول البوابة يجب أن يكون رقم صالح');
        }
        return num;
      })
    ], {
      required_error: 'رقم دخول البوابة مطلوب',
    }),
    declarationType: z.enum(['IMPORT', 'EXPORT'], {
      required_error: 'نوع البيان مطلوب',
    }),
    declarationDate: z.string().optional().transform((val) => (val ? new Date(val) : undefined)),
    count: z.union([z.number(), z.string().transform(val => val ? parseFloat(val) : undefined)]).optional(),
    weight: z.union([z.string(), z.number().transform(val => val.toString())]).optional(),
    goodsType: z.string().optional(),
    itemsCount: z.union([z.number(), z.string().transform(val => val ? parseFloat(val) : undefined)]).optional(),
    entryDate: z.string().optional().transform((val) => (val ? new Date(val) : undefined)),
    exitDate: z.string().optional().transform((val) => (val ? new Date(val) : undefined)),
    clientId: z.string().uuid().optional(),
    drivers: z
      .array(
        z.object({
          name: z.string({
            required_error: 'اسم السائق مطلوب',
          }),
          truckNumber: z.string().optional(),
          trailerNumber: z.string().optional(),
          phoneNumber: z.string().optional(),
        })
      )
      .optional(),
  }),
});

// مخطط تحديث البيان
export const updateDeclarationSchema = z.object({
  params: z.object({
    id: z.string({
      required_error: 'معرف البيان مطلوب',
    }),
  }),
  body: z.object({
    taxNumber: z.string().optional(),
    clientName: z.string().optional(),
    companyName: z.string().optional(),
    policyNumber: z.union([z.number(), z.string().transform(val => val ? parseFloat(val) : undefined)]).optional(),
    invoiceNumber: z.union([z.number(), z.string().transform(val => val ? parseFloat(val) : undefined)]).optional(),
    gatewayEntryNumber: z.union([z.number(), z.string().transform(val => val ? parseFloat(val) : undefined)]).optional(),
    declarationType: z.enum(['IMPORT', 'EXPORT']).optional(),
    declarationDate: z.string().optional().transform((val) => (val ? new Date(val) : undefined)),
    count: z.union([z.number(), z.string().transform(val => val ? parseFloat(val) : undefined)]).optional(),
    weight: z.union([z.string(), z.number().transform(val => val.toString())]).optional(),
    goodsType: z.string().optional(),
    itemsCount: z.union([z.number(), z.string().transform(val => val ? parseFloat(val) : undefined)]).optional(),
    entryDate: z.string().optional().transform((val) => (val ? new Date(val) : undefined)),
    exitDate: z.string().optional().transform((val) => (val ? new Date(val) : undefined)),
    clientId: z.string().uuid().optional(),
    drivers: z
      .array(
        z.object({
          id: z.string().uuid().optional(),
          name: z.string(),
          truckNumber: z.string().optional(),
          trailerNumber: z.string().optional(),
          phoneNumber: z.string().optional(),
        })
      )
      .optional(),
  }),
});

// مخطط الحصول على البيان
export const getDeclarationSchema = z.object({
  params: z.object({
    id: z.string({
      required_error: 'معرف البيان مطلوب',
    }),
  }),
});

// مخطط حذف البيان
export const deleteDeclarationSchema = z.object({
  params: z.object({
    id: z.string({
      required_error: 'معرف البيان مطلوب',
    }),
  }),
});

// مخطط قائمة البيانات
export const listDeclarationsSchema = z.object({
  query: z.object({
    page: z.string().optional().transform((val) => (val ? parseInt(val) : 1)),
    limit: z.string().optional().transform((val) => (val ? parseInt(val) : 10)),
    sort: z.string().optional().default('declarationNumber'),
    order: z.enum(['asc', 'desc']).optional().default('desc'),
    search: z.string().optional(),
    declarationType: z.enum(['IMPORT', 'EXPORT']).optional(),
    fromDate: z.string().optional().transform((val) => (val ? new Date(val) : undefined)),
    toDate: z.string().optional().transform((val) => (val ? new Date(val) : undefined)),
    clientId: z.string().uuid().optional(),
  }),
});
