-- CreateEnum
CREATE TYPE "UserRole" AS ENUM ('ADMIN', 'USER', 'MANAGER');

-- CreateEnum
CREATE TYPE "DeclarationType" AS ENUM ('IMPORT', 'EXPORT');

-- CreateEnum
CREATE TYPE "GoodsType" AS ENUM ('HUMAN_MEDICINE', 'LABORATORY_SOLUTIONS', 'MEDICAL_SUPPLIES', 'SUGAR_STRIPS', 'MEDICAL_DEVICES', 'MISCELLANEOUS');

-- CreateEnum
CREATE TYPE "PackageType" AS ENUM ('DRUM', 'CARTON', 'BARREL');

-- CreateEnum
CREATE TYPE "GuaranteeType" AS ENUM ('DOCUMENTS', 'FINANCIAL');

-- CreateEnum
CREATE TYPE "ReceiptType" AS ENUM ('FOLLOW_UP', 'CLEARANCE', 'RECEIPT', 'DELIVERY');

-- Create<PERSON>num
CREATE TYPE "DocumentType" AS ENUM ('INVOICE', 'CERTIFICATE', 'PERMIT', 'AUTH<PERSON><PERSON><PERSON>AT<PERSON>', 'GUARANTEE', 'RECEIPT', 'RELEASE', 'OTHER');

-- CreateEnum
CREATE TYPE "AuthorizationType" AS ENUM ('FOLLOW_UP', 'CLEARANCE', 'RECEIPT', 'FULL');

-- CreateEnum
CREATE TYPE "Currency" AS ENUM ('USD', 'EUR', 'GBP', 'SAR');

-- CreateEnum
CREATE TYPE "GuaranteeStatus" AS ENUM ('ACTIVE', 'RETURNED', 'EXPIRED');

-- CreateEnum
CREATE TYPE "TokenType" AS ENUM ('ACCESS', 'REFRESH');

-- CreateEnum
CREATE TYPE "LoginStatus" AS ENUM ('SUCCESS', 'FAILED', 'LOCKED', 'SUSPICIOUS');

-- CreateTable
CREATE TABLE "users" (
    "id" TEXT NOT NULL,
    "username" TEXT NOT NULL,
    "email" TEXT NOT NULL,
    "password" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "role" "UserRole" NOT NULL DEFAULT 'USER',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "isActive" BOOLEAN NOT NULL DEFAULT true,

    CONSTRAINT "users_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "clients" (
    "id" TEXT NOT NULL,
    "clientNumber" TEXT,
    "taxNumber" TEXT NOT NULL,
    "clientName" TEXT NOT NULL,
    "companyName" TEXT,
    "addedDate" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "phone" TEXT,
    "email" TEXT,
    "address" TEXT,
    "pdfFile" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "clients_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "declarations" (
    "id" TEXT NOT NULL,
    "declarationNumber" TEXT NOT NULL,
    "taxNumber" TEXT NOT NULL,
    "clientName" TEXT NOT NULL,
    "companyName" TEXT,
    "policyNumber" TEXT,
    "invoiceNumber" TEXT,
    "gatewayEntryNumber" TEXT NOT NULL,
    "declarationType" "DeclarationType" NOT NULL,
    "declarationDate" TIMESTAMP(3) NOT NULL,
    "count" INTEGER,
    "weight" DOUBLE PRECISION,
    "goodsType" "GoodsType",
    "itemsCount" INTEGER,
    "entryDate" TIMESTAMP(3),
    "exitDate" TIMESTAMP(3),
    "pdfFile" TEXT,
    "clientId" TEXT,
    "userId" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "declarations_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "drivers" (
    "id" TEXT NOT NULL,
    "declarationId" TEXT NOT NULL,
    "driverName" TEXT NOT NULL,
    "truckNumber" TEXT NOT NULL,
    "trailerNumber" TEXT,
    "driverPhone" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "drivers_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "item_movements" (
    "id" TEXT NOT NULL,
    "movementNumber" TEXT NOT NULL,
    "movementDate" TIMESTAMP(3) NOT NULL,
    "declarationNumber" TEXT NOT NULL,
    "itemNumber" TEXT,
    "invoiceNumber" TEXT NOT NULL,
    "packingListNumber" TEXT,
    "tariffCode" TEXT,
    "itemName" TEXT NOT NULL,
    "quantity" INTEGER NOT NULL,
    "packageType" "PackageType",
    "goodsType" "GoodsType",
    "countryOfOrigin" TEXT,
    "itemValue" DOUBLE PRECISION,
    "currency" "Currency",
    "totalValue" DOUBLE PRECISION,
    "pdfFile" TEXT,
    "declarationId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "item_movements_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "authorizations" (
    "id" TEXT NOT NULL,
    "authorizationNumber" TEXT NOT NULL,
    "clientName" TEXT NOT NULL,
    "taxNumber" TEXT NOT NULL,
    "authorizationType" "AuthorizationType" NOT NULL,
    "startDate" TIMESTAMP(3) NOT NULL,
    "endDate" TIMESTAMP(3) NOT NULL,
    "pdfFile" TEXT,
    "declarationId" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "authorizations_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "releases" (
    "id" TEXT NOT NULL,
    "releaseNumber" TEXT NOT NULL,
    "issuingAuthority" TEXT NOT NULL,
    "invoiceNumber" TEXT NOT NULL,
    "invoiceDate" TIMESTAMP(3),
    "invoiceValue" DOUBLE PRECISION,
    "approvalDate" TIMESTAMP(3) NOT NULL,
    "releaseStartDate" TIMESTAMP(3) NOT NULL,
    "releaseEndDate" TIMESTAMP(3) NOT NULL,
    "driverPermit" BOOLEAN NOT NULL DEFAULT false,
    "pdfFile" TEXT,
    "declarationId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "releases_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "permits" (
    "id" TEXT NOT NULL,
    "permitNumber" TEXT NOT NULL,
    "declarationNumber" TEXT,
    "issuingAuthority" TEXT NOT NULL,
    "permitDate" TIMESTAMP(3) NOT NULL,
    "pdfFile" TEXT,
    "declarationId" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "permits_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "returnable_guarantees" (
    "id" TEXT NOT NULL,
    "guaranteeSlipNumber" TEXT NOT NULL,
    "declarationNumber" TEXT,
    "guaranteeType" "GuaranteeType" NOT NULL,
    "guaranteeStartDate" TIMESTAMP(3) NOT NULL,
    "guaranteeEndDate" TIMESTAMP(3) NOT NULL,
    "clientName" TEXT NOT NULL,
    "invoiceValue" DOUBLE PRECISION,
    "invoiceNumber" TEXT,
    "originCertNumber" TEXT,
    "packingListNumber" TEXT,
    "countryOfOrigin" TEXT,
    "declarationValue" DOUBLE PRECISION,
    "guaranteeAmount" DOUBLE PRECISION,
    "guaranteeDate" TIMESTAMP(3),
    "invoiceDate" TIMESTAMP(3),
    "pdfFile" TEXT,
    "declarationId" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "returnable_guarantees_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "non_returnable_guarantees" (
    "id" TEXT NOT NULL,
    "bankSlipNumber" TEXT NOT NULL,
    "declarationNumber" TEXT,
    "clientName" TEXT NOT NULL,
    "confiscationDate" TIMESTAMP(3),
    "confiscatedAmount" DOUBLE PRECISION,
    "confiscationReason" TEXT,
    "notes" TEXT,
    "pdfFile" TEXT,
    "declarationId" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "non_returnable_guarantees_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "receipts" (
    "id" TEXT NOT NULL,
    "receiptNumber" TEXT NOT NULL,
    "declarationNumber" TEXT,
    "receiptType" "ReceiptType",
    "invoiceDate" TIMESTAMP(3),
    "invoiceValue" DOUBLE PRECISION,
    "pdfFile" TEXT,
    "declarationId" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "receipts_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "office_documents" (
    "id" TEXT NOT NULL,
    "documentNumber" TEXT NOT NULL,
    "documentType" "DocumentType",
    "documentDate" TIMESTAMP(3) NOT NULL,
    "documentValue" DOUBLE PRECISION,
    "pdfFile" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "office_documents_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "invalidated_tokens" (
    "id" TEXT NOT NULL,
    "token" TEXT NOT NULL,
    "tokenType" "TokenType" NOT NULL,
    "userId" TEXT NOT NULL,
    "expiresAt" TIMESTAMP(3) NOT NULL,
    "invalidatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "invalidated_tokens_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "sessions" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "ipAddress" TEXT,
    "userAgent" TEXT,
    "deviceInfo" TEXT,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "lastActivity" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "expiresAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "sessions_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "login_attempts" (
    "id" TEXT NOT NULL,
    "username" TEXT NOT NULL,
    "ipAddress" TEXT,
    "userAgent" TEXT,
    "status" "LoginStatus" NOT NULL,
    "attemptTime" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "failureReason" TEXT,

    CONSTRAINT "login_attempts_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "audit_logs" (
    "id" TEXT NOT NULL,
    "tableName" TEXT NOT NULL,
    "operation" TEXT NOT NULL,
    "action" TEXT,
    "recordId" TEXT NOT NULL,
    "oldValues" JSONB,
    "newValues" JSONB,
    "userId" TEXT,
    "ipAddress" TEXT,
    "userAgent" TEXT,
    "timestamp" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "audit_logs_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "custom_forms" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "formData" JSONB NOT NULL,
    "formType" TEXT,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "userId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "custom_forms_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "report_templates" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "template" JSONB NOT NULL,
    "reportType" TEXT NOT NULL,
    "isDefault" BOOLEAN NOT NULL DEFAULT false,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "userId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "report_templates_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "system_settings" (
    "id" TEXT NOT NULL DEFAULT 'default',
    "companyName" TEXT NOT NULL DEFAULT 'نظام النور للأرشفة',
    "companyLogo" TEXT,
    "companyAddress" TEXT,
    "companyPhone" TEXT,
    "companyEmail" TEXT,
    "companyWebsite" TEXT,
    "primaryColor" TEXT NOT NULL DEFAULT '#1976d2',
    "secondaryColor" TEXT NOT NULL DEFAULT '#dc004e',
    "defaultFont" TEXT NOT NULL DEFAULT 'Tajawal',
    "defaultLanguage" TEXT NOT NULL DEFAULT 'ar',
    "maxFileSize" INTEGER NOT NULL DEFAULT *********,
    "enablePrinting" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "system_settings_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "users_username_key" ON "users"("username");

-- CreateIndex
CREATE UNIQUE INDEX "users_email_key" ON "users"("email");

-- CreateIndex
CREATE UNIQUE INDEX "clients_taxNumber_key" ON "clients"("taxNumber");

-- CreateIndex
CREATE INDEX "clients_clientName_idx" ON "clients"("clientName");

-- CreateIndex
CREATE INDEX "clients_phone_idx" ON "clients"("phone");

-- CreateIndex
CREATE INDEX "clients_email_idx" ON "clients"("email");

-- CreateIndex
CREATE INDEX "clients_createdAt_idx" ON "clients"("createdAt");

-- CreateIndex
CREATE UNIQUE INDEX "declarations_declarationNumber_key" ON "declarations"("declarationNumber");

-- CreateIndex
CREATE INDEX "declarations_taxNumber_idx" ON "declarations"("taxNumber");

-- CreateIndex
CREATE INDEX "declarations_clientName_idx" ON "declarations"("clientName");

-- CreateIndex
CREATE INDEX "declarations_companyName_idx" ON "declarations"("companyName");

-- CreateIndex
CREATE INDEX "declarations_invoiceNumber_idx" ON "declarations"("invoiceNumber");

-- CreateIndex
CREATE INDEX "declarations_declarationType_idx" ON "declarations"("declarationType");

-- CreateIndex
CREATE INDEX "declarations_declarationDate_idx" ON "declarations"("declarationDate");

-- CreateIndex
CREATE INDEX "declarations_goodsType_idx" ON "declarations"("goodsType");

-- CreateIndex
CREATE INDEX "declarations_clientId_idx" ON "declarations"("clientId");

-- CreateIndex
CREATE INDEX "declarations_userId_idx" ON "declarations"("userId");

-- CreateIndex
CREATE INDEX "declarations_createdAt_idx" ON "declarations"("createdAt");

-- CreateIndex
CREATE INDEX "declarations_entryDate_idx" ON "declarations"("entryDate");

-- CreateIndex
CREATE INDEX "declarations_exitDate_idx" ON "declarations"("exitDate");

-- CreateIndex
CREATE UNIQUE INDEX "item_movements_movementNumber_key" ON "item_movements"("movementNumber");

-- CreateIndex
CREATE INDEX "item_movements_movementDate_idx" ON "item_movements"("movementDate");

-- CreateIndex
CREATE INDEX "item_movements_declarationNumber_idx" ON "item_movements"("declarationNumber");

-- CreateIndex
CREATE INDEX "item_movements_itemName_idx" ON "item_movements"("itemName");

-- CreateIndex
CREATE INDEX "item_movements_invoiceNumber_idx" ON "item_movements"("invoiceNumber");

-- CreateIndex
CREATE INDEX "item_movements_goodsType_idx" ON "item_movements"("goodsType");

-- CreateIndex
CREATE INDEX "item_movements_declarationId_idx" ON "item_movements"("declarationId");

-- CreateIndex
CREATE INDEX "item_movements_createdAt_idx" ON "item_movements"("createdAt");

-- CreateIndex
CREATE UNIQUE INDEX "authorizations_authorizationNumber_key" ON "authorizations"("authorizationNumber");

-- CreateIndex
CREATE INDEX "authorizations_clientName_idx" ON "authorizations"("clientName");

-- CreateIndex
CREATE INDEX "authorizations_taxNumber_idx" ON "authorizations"("taxNumber");

-- CreateIndex
CREATE INDEX "authorizations_authorizationType_idx" ON "authorizations"("authorizationType");

-- CreateIndex
CREATE INDEX "authorizations_startDate_idx" ON "authorizations"("startDate");

-- CreateIndex
CREATE INDEX "authorizations_endDate_idx" ON "authorizations"("endDate");

-- CreateIndex
CREATE INDEX "authorizations_declarationId_idx" ON "authorizations"("declarationId");

-- CreateIndex
CREATE INDEX "authorizations_createdAt_idx" ON "authorizations"("createdAt");

-- CreateIndex
CREATE UNIQUE INDEX "releases_releaseNumber_key" ON "releases"("releaseNumber");

-- CreateIndex
CREATE INDEX "releases_invoiceNumber_idx" ON "releases"("invoiceNumber");

-- CreateIndex
CREATE INDEX "releases_approvalDate_idx" ON "releases"("approvalDate");

-- CreateIndex
CREATE INDEX "releases_releaseStartDate_idx" ON "releases"("releaseStartDate");

-- CreateIndex
CREATE INDEX "releases_releaseEndDate_idx" ON "releases"("releaseEndDate");

-- CreateIndex
CREATE INDEX "releases_declarationId_idx" ON "releases"("declarationId");

-- CreateIndex
CREATE INDEX "releases_createdAt_idx" ON "releases"("createdAt");

-- CreateIndex
CREATE UNIQUE INDEX "permits_permitNumber_key" ON "permits"("permitNumber");

-- CreateIndex
CREATE INDEX "permits_declarationNumber_idx" ON "permits"("declarationNumber");

-- CreateIndex
CREATE INDEX "permits_permitDate_idx" ON "permits"("permitDate");

-- CreateIndex
CREATE INDEX "permits_declarationId_idx" ON "permits"("declarationId");

-- CreateIndex
CREATE INDEX "permits_createdAt_idx" ON "permits"("createdAt");

-- CreateIndex
CREATE UNIQUE INDEX "returnable_guarantees_guaranteeSlipNumber_key" ON "returnable_guarantees"("guaranteeSlipNumber");

-- CreateIndex
CREATE UNIQUE INDEX "non_returnable_guarantees_bankSlipNumber_key" ON "non_returnable_guarantees"("bankSlipNumber");

-- CreateIndex
CREATE UNIQUE INDEX "receipts_receiptNumber_key" ON "receipts"("receiptNumber");

-- CreateIndex
CREATE UNIQUE INDEX "office_documents_documentNumber_key" ON "office_documents"("documentNumber");

-- CreateIndex
CREATE UNIQUE INDEX "invalidated_tokens_token_key" ON "invalidated_tokens"("token");

-- CreateIndex
CREATE INDEX "invalidated_tokens_expiresAt_idx" ON "invalidated_tokens"("expiresAt");

-- CreateIndex
CREATE INDEX "sessions_userId_idx" ON "sessions"("userId");

-- CreateIndex
CREATE INDEX "sessions_expiresAt_idx" ON "sessions"("expiresAt");

-- CreateIndex
CREATE INDEX "login_attempts_username_idx" ON "login_attempts"("username");

-- CreateIndex
CREATE INDEX "login_attempts_ipAddress_idx" ON "login_attempts"("ipAddress");

-- CreateIndex
CREATE INDEX "login_attempts_attemptTime_idx" ON "login_attempts"("attemptTime");

-- CreateIndex
CREATE INDEX "audit_logs_tableName_idx" ON "audit_logs"("tableName");

-- CreateIndex
CREATE INDEX "audit_logs_operation_idx" ON "audit_logs"("operation");

-- CreateIndex
CREATE INDEX "audit_logs_timestamp_idx" ON "audit_logs"("timestamp");

-- CreateIndex
CREATE UNIQUE INDEX "custom_forms_name_key" ON "custom_forms"("name");

-- AddForeignKey
ALTER TABLE "declarations" ADD CONSTRAINT "declarations_clientId_fkey" FOREIGN KEY ("clientId") REFERENCES "clients"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "declarations" ADD CONSTRAINT "declarations_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "drivers" ADD CONSTRAINT "drivers_declarationId_fkey" FOREIGN KEY ("declarationId") REFERENCES "declarations"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "item_movements" ADD CONSTRAINT "item_movements_declarationId_fkey" FOREIGN KEY ("declarationId") REFERENCES "declarations"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "authorizations" ADD CONSTRAINT "authorizations_declarationId_fkey" FOREIGN KEY ("declarationId") REFERENCES "declarations"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "releases" ADD CONSTRAINT "releases_declarationId_fkey" FOREIGN KEY ("declarationId") REFERENCES "declarations"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "permits" ADD CONSTRAINT "permits_declarationId_fkey" FOREIGN KEY ("declarationId") REFERENCES "declarations"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "returnable_guarantees" ADD CONSTRAINT "returnable_guarantees_declarationId_fkey" FOREIGN KEY ("declarationId") REFERENCES "declarations"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "non_returnable_guarantees" ADD CONSTRAINT "non_returnable_guarantees_declarationId_fkey" FOREIGN KEY ("declarationId") REFERENCES "declarations"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "receipts" ADD CONSTRAINT "receipts_declarationId_fkey" FOREIGN KEY ("declarationId") REFERENCES "declarations"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "sessions" ADD CONSTRAINT "sessions_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "custom_forms" ADD CONSTRAINT "custom_forms_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "report_templates" ADD CONSTRAINT "report_templates_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
