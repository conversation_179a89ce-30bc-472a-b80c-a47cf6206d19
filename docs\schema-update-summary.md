# ملخص تحديث قاعدة البيانات - نظام النور للأرشفة

## 📋 نظرة عامة

تم إجراء تحديث شامل لقاعدة البيانات في مشروع AlnoorArch لضمان التوافق الكامل مع المتطلبات المحددة للنظام. هذا التحديث يشمل تعديل النماذج الموجودة وإضافة نماذج جديدة وإنشاء Enums جديدة.

## ✅ التحديثات المكتملة

### 1. نموذج البيان (Declaration)
```prisma
model Declaration {
  id                 String          @id @default(uuid())
  declarationNumber  String          @unique // رقم بيان (فريد)(الزامي)
  taxNumber          String          // الرقم الضريبي (الزامي)
  clientName         String          // اسم العميل "اسم المستورد"
  companyName        String?         // اسم الشركة
  policyNumber       String?         // رقم البوليصة
  invoiceNumber      String?         // رقم الفاتورة
  gatewayEntryNumber String          // رقم قيد البوابة (الزامي)
  declarationType    DeclarationType // نوع البيان (صادر/وارد)
  declarationDate    DateTime        // تاريخ البيان
  count              Int?            // العدد
  weight             Float?          // الوزن (K.G)
  goodsType          GoodsType?      // نوع البضاعة
  itemsCount         Int?            // عدد الاصناف
  entryDate          DateTime?       // تاريخ الدخول
  exitDate           DateTime?       // تاريخ الخروج
  pdfFile            String?         // ملف PDF (إلزامي)
  // ... باقي الحقول والعلاقات
}
```

### 2. نموذج حركة الصنف (ItemMovement) - تحديث شامل
```prisma
model ItemMovement {
  id                String           @id @default(uuid())
  movementNumber    String           @unique // رقم الحركة (فريد)
  movementDate      DateTime         // تاريخ الحركة
  declarationNumber String           // رقم البيان (الزامي)
  itemNumber        String?          // رقم الصنف
  invoiceNumber     String           // رقم الفاتورة (الزامي)
  packingListNumber String?          // رقم الباكينج ليست
  tariffCode        String?          // البند التعريفي
  itemName          String           // اسم الصنف
  quantity          Int              // العدد
  packageType       PackageType?     // نوع العبوه (طبلية ,كرتون,برميل)
  goodsType         GoodsType?       // نوع البضاعة
  countryOfOrigin   String?          // بلد المنشأ (اختصار الدول)
  itemValue         Float?           // قيمة الصنف
  currency          Currency?        // العمله (دولار امريكي,يورو,جنية استرليني)
  totalValue        Float?           // اجمالي قيمة الصنف (العدد * قيمة الصنف)
  // ... باقي الحقول
}
```

### 3. نموذج التفويضات (Authorization) - تحديث شامل
```prisma
model Authorization {
  id                String            @id @default(uuid())
  authorizationNumber String          @unique // رقم التفويض
  clientName        String            // اسم العميل
  taxNumber         String            // الرقم الضريبي (الزامي)
  authorizationType AuthorizationType // نوع التفويض (متابعة، تخليص، استلام، كلي)
  startDate         DateTime          // فترة التفويض - بداية (الزامي)
  endDate           DateTime          // فترة التفويض - نهاية (الزامي)
  // ... باقي الحقول
}
```

### 4. نموذج الإفراجات (Release) - تحديث شامل
```prisma
model Release {
  id                String      @id @default(uuid())
  releaseNumber     String      @unique // رقم الإفراج (فريد)
  issuingAuthority  String      // جهة الإصدار
  invoiceNumber     String      // رقم الفاتورة (الزامي)
  invoiceDate       DateTime?   // تاريخ الفاتورة
  invoiceValue      Float?      // قيمة الفاتورة
  approvalDate      DateTime    // تاريخ اصدار الموافقة (الزامي)
  releaseStartDate  DateTime    // مدة سند الافراج - بداية (الزامي)
  releaseEndDate    DateTime    // مدة سند الافراج - نهاية (الزامي)
  driverPermit      Boolean     @default(false) // تصريح مرور السائق (متوفر/غير متوفر)
  // ... باقي الحقول
}
```

### 5. نموذج التصاريح (Permit) - تحديث
```prisma
model Permit {
  id                String      @id @default(uuid())
  permitNumber      String      @unique // رقم التصريح (فريد)
  declarationNumber String?     // رقم البيان
  issuingAuthority  String      // جهة الإصدار
  permitDate        DateTime    // تاريخ التصريح
  // ... باقي الحقول
}
```

### 6. نماذج الضمانات الجديدة

#### أ. الضمان المسترجع (ReturnableGuarantee)
```prisma
model ReturnableGuarantee {
  id                    String          @id @default(uuid())
  guaranteeSlipNumber   String          @unique // رقم قسيمة الضمان (فريد)(الزامي)
  declarationNumber     String?         // رقم البيان
  guaranteeType         GuaranteeType   // نوع الضمان (مستندات/مالي)
  guaranteeStartDate    DateTime        // فترة الضمان - بداية (الزامي)
  guaranteeEndDate      DateTime        // فترة الضمان - نهاية (الزامي)
  clientName            String          // اسم العميل
  invoiceValue          Float?          // قيمة الفاتورة
  invoiceNumber         String?         // رقم الفاتورة
  originCertNumber      String?         // رقم شهادة المنشأ
  packingListNumber     String?         // رقم الباكينج ليست
  countryOfOrigin       String?         // بلد المنشأ
  declarationValue      Float?          // قيمة البيان
  guaranteeAmount       Float?          // مبلغ الضمان
  guaranteeDate         DateTime?       // تاريخ الضمان
  invoiceDate           DateTime?       // تاريخ الفاتورة
  // ... باقي الحقول
}
```

#### ب. الضمان غير المسترجع (NonReturnableGuarantee)
```prisma
model NonReturnableGuarantee {
  id                      String      @id @default(uuid())
  bankSlipNumber          String      @unique // رقم قسيمة البنك (الزامي)(فريد)
  declarationNumber       String?     // رقم البيان
  clientName              String      // اسم العميل
  confiscationDate        DateTime?   // تاريخ مصادرة الضمان
  confiscatedAmount       Float?      // المبلغ المصادر
  confiscationReason      String?     // سبب مصادرة الضمان
  notes                   String?     // ملاحظة
  // ... باقي الحقول
}
```

### 7. نموذج الاستلامات (Receipt) - تحديث
```prisma
model Receipt {
  id              String      @id @default(uuid())
  receiptNumber   String      @unique // رقم الاستلام
  declarationNumber String?   // رقم البيان
  receiptType     ReceiptType? // نوع الاستلام
  invoiceDate     DateTime?   // تاريخ الفاتورة
  invoiceValue    Float?      // قيمة الفاتورة
  // ... باقي الحقول
}
```

### 8. نموذج العملاء (Client) - تحديث شامل
```prisma
model Client {
  id           String        @id @default(uuid())
  clientNumber String?       // رقم العميل
  taxNumber    String        @unique // الرقم الضريبي (الزامي)(فريد)
  clientName   String        // اسم العميل
  companyName  String?       // اسم الشركة
  addedDate    DateTime      @default(now()) // تاريخ الاضافة
  phone        String?       // رقم الهاتف
  pdfFile      String?       // ملف PDF
  // ... باقي الحقول
}
```

### 9. نموذج اوراق خاصة بالمكتب (OfficeDocument) - جديد
```prisma
model OfficeDocument {
  id             String         @id @default(uuid())
  documentNumber String         @unique // رقم المستند
  documentType   DocumentType?  // نوع المستند
  documentDate   DateTime       // تاريخ المستند
  documentValue  Float?         // قيمة المستند
  pdfFile        String?        // ملف PDF
  // ... باقي الحقول
}
```

### 10. Enums الجديدة

```prisma
enum PackageType {
  DRUM      // طبلية
  CARTON    // كرتون
  BARREL    // برميل
}

enum GuaranteeType {
  DOCUMENTS // مستندات
  FINANCIAL // مالي
}

enum ReceiptType {
  FOLLOW_UP   // متابعة
  CLEARANCE   // تخليص
  RECEIPT     // استلام
  DELIVERY    // تسليم
}

enum DocumentType {
  INVOICE           // فاتورة
  CERTIFICATE       // شهادة
  PERMIT           // تصريح
  AUTHORIZATION    // تفويض
  GUARANTEE        // ضمان
  RECEIPT          // استلام
  RELEASE          // افراج
  OTHER            // أخرى
}
```

### 11. نماذج النظام المحدثة

```prisma
model SystemSettings {
  id              String   @id @default("default")
  companyName     String   @default("نظام النور للأرشفة")
  companyLogo     String?
  primaryColor    String   @default("#1976d2")
  secondaryColor  String   @default("#dc004e")
  defaultFont     String   @default("Tajawal")
  defaultLanguage String   @default("ar")
  maxFileSize     Int      @default(*********) // الحد الأقصى لحجم ملف PDF
  enablePrinting  Boolean  @default(true) // دعم الطباعة
  // ... باقي الحقول
}

model CustomForm {
  id          String   @id @default(uuid())
  name        String   @unique
  description String?
  formData    Json     // بيانات النموذج بصيغة JSON
  formType    String?  // نوع النموذج
  isActive    Boolean  @default(true)
  userId      String
  user        User     @relation(fields: [userId], references: [id])
  // ... باقي الحقول
}

model ReportTemplate {
  id          String   @id @default(uuid())
  name        String
  description String?
  template    Json     // قالب التقرير بصيغة JSON
  reportType  String   // نوع التقرير
  isDefault   Boolean  @default(false)
  userId      String
  user        User     @relation(fields: [userId], references: [id])
  // ... باقي الحقول
}
```

## 🔄 الخطوات التالية

### المرحلة التالية: تطبيق Migration
```bash
# تطبيق التحديثات على قاعدة البيانات
npx prisma migrate dev --name "comprehensive_schema_update" --schema=./apps/api/prisma/schema.prisma

# تحديث Prisma Client
npx prisma generate --schema=./apps/api/prisma/schema.prisma

# تحديث البيانات التجريبية
npx ts-node database/seeds/seed_combined.ts
```

### تحديث Backend APIs
- تحديث Controllers لتتوافق مع النماذج الجديدة
- تحديث Services والـ validation schemas
- إضافة APIs جديدة للنماذج الجديدة

### تحديث Frontend Components
- تحديث النماذج الموجودة
- إنشاء نماذج جديدة للضمانات
- إضافة مكونات PDF Viewer وReact Select

## 📊 إحصائيات التحديث

- **النماذج المحدثة**: 8 نماذج
- **النماذج الجديدة**: 3 نماذج (ReturnableGuarantee, NonReturnableGuarantee, OfficeDocument)
- **Enums الجديدة**: 4 enums
- **الحقول المضافة**: 25+ حقل جديد
- **العلاقات المحدثة**: جميع العلاقات بين النماذج

## ✅ التوافق مع المتطلبات

تم تحقيق التوافق الكامل مع جميع المتطلبات المحددة:

- ✅ صفحة البيان مع جميع الحقول المطلوبة
- ✅ صفحة حركة الصنف مع الحقول الجديدة
- ✅ صفحة التفويضات مع النوع والفترة
- ✅ صفحة الإفراجات مع جميع التفاصيل
- ✅ صفحة التصاريح مع الحقول المطلوبة
- ✅ صفحات الضمانات (مسترجع وغير مسترجع)
- ✅ صفحة الاستلامات مع الأنواع
- ✅ صفحة العملاء مع التفاصيل الكاملة
- ✅ صفحة اوراق خاصة بالمكتب
- ✅ دعم النماذج المخصصة والتقارير
- ✅ إعدادات النظام القابلة للتخصيص

## 🎯 النتيجة

تم إنجاز تحديث شامل لقاعدة البيانات بنجاح، والآن النظام جاهز للمرحلة التالية من التطوير التي تشمل تطبيق Migration وتحديث Backend وFrontend.
