import { prisma } from '../../../core/utils/prisma.js';
import { HttpException } from '../../../core/middleware/error.middleware.js';
import { Prisma } from '@prisma/client';
import { CreateItemMovementInput, UpdateItemMovementInput, ItemMovementFilters, ItemMovementListOptions } from '../types/item-movement.types.js';

interface ListItemMovementsParams {
  page?: number;
  limit?: number;
  sort?: string;
  order?: 'asc' | 'desc';
  search?: string;
  declarationId?: string;
  fromDate?: Date;
  toDate?: Date;
}

export const itemMovementService = {
  /**
   * إنشاء حركة صنف جديدة
   */
  createItemMovement: async (
    data: CreateItemMovementInput
  ) => {
    try {
      // التحقق من وجود البيان
      const declaration = await prisma.declaration.findUnique({
        where: { id: data.declarationId },
      });

      if (!declaration) {
        throw new HttpException(404, 'البيان غير موجود', 'Not Found');
      }

      // إنشاء رقم الحركة
      const movementNumber = `MOV-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

      // إنشاء حركة الصنف
      const itemMovement = await prisma.itemMovement.create({
        data: {
          itemName: data.itemName,
          quantity: data.quantity,
          unit: data.unit,
          movementDate: data.movementDate,
          movementType: data.movementType,
          notes: data.notes,
          declarationId: data.declarationId,
        },
      });

      // إرجاع حركة الصنف
      return prisma.itemMovement.findUnique({
        where: { id: itemMovement.id },
        include: {
          declaration: true,
        },
      });
    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError) {
        if (error.code === 'P2002') {
          throw new HttpException(409, 'حركة الصنف موجودة بالفعل', 'Conflict');
        }
      }
      throw error;
    }
  },

  /**
   * تحديث حركة صنف
   */
  updateItemMovement: async (
    id: string,
    data: UpdateItemMovementInput
  ) => {
    try {
      // التحقق من وجود حركة الصنف
      const existingItemMovement = await prisma.itemMovement.findUnique({
        where: { id },
      });

      if (!existingItemMovement) {
        throw new HttpException(404, 'حركة الصنف غير موجودة', 'Not Found');
      }

      // تحديث حركة الصنف
      const updatedItemMovement = await prisma.itemMovement.update({
        where: { id },
        data: {
          itemName: data.itemName,
          quantity: data.quantity,
          movementDate: data.movementDate,
        },
        include: {
          declaration: true,
        },
      });

      return updatedItemMovement;
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(500, 'حدث خطأ أثناء تحديث حركة الصنف', 'Internal Server Error');
    }
  },

  /**
   * الحصول على حركة صنف محددة
   */
  getItemMovement: async (id: string) => {
    const itemMovement = await prisma.itemMovement.findUnique({
      where: { id },
      include: {
        declaration: true,
      },
    });

    if (!itemMovement) {
      throw new HttpException(404, 'حركة الصنف غير موجودة', 'Not Found');
    }

    return itemMovement;
  },

  /**
   * حذف حركة صنف
   */
  deleteItemMovement: async (id: string) => {
    // التحقق من وجود حركة الصنف
    const itemMovement = await prisma.itemMovement.findUnique({
      where: { id },
    });

    if (!itemMovement) {
      throw new HttpException(404, 'حركة الصنف غير موجودة', 'Not Found');
    }

    // حذف حركة الصنف
    const deletedItemMovement = await prisma.itemMovement.delete({
      where: { id },
    });

    return deletedItemMovement;
  },

  /**
   * الحصول على قائمة حركات الأصناف
   */
  listItemMovements: async (params: ListItemMovementsParams) => {
    const {
      page = 1,
      limit = 10,
      sort = 'createdAt',
      order = 'desc',
      search,
      declarationId,
      fromDate,
      toDate,
    } = params;

    // بناء شروط البحث
    const where: Prisma.ItemMovementWhereInput = {};

    if (search) {
      where.OR = [
        { itemName: { contains: search } },
        { movementType: { contains: search } },
      ];
    }

    if (declarationId) {
      where.declarationId = declarationId;
    }

    if (fromDate && toDate) {
      where.movementDate = {
        gte: fromDate,
        lte: toDate,
      };
    } else if (fromDate) {
      where.movementDate = {
        gte: fromDate,
      };
    } else if (toDate) {
      where.movementDate = {
        lte: toDate,
      };
    }

    // حساب إجمالي عدد حركات الأصناف
    const total = await prisma.itemMovement.count({ where });

    // الحصول على حركات الأصناف
    const itemMovements = await prisma.itemMovement.findMany({
      where,
      include: {
        declaration: true,
      },
      orderBy: {
        [sort]: order,
      },
      skip: (page - 1) * limit,
      take: limit,
    });

    return {
      data: itemMovements,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    };
  },
};
