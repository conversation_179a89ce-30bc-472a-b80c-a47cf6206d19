import { prismaMock } from '../../../../core/utils/__mocks__/prisma.js';
import { HttpException } from '../../../../core/middleware/error.middleware.js';
import { UserRole } from '@prisma/client';

// Mock the prisma module
const mockPrisma = prismaMock;

// Mock the entire auth service
const mockAuthService = {
  login: async (username: string, password: string, request: any) => {
    // التحقق من وجود المستخدم
    const user = await mockPrisma.user.findUnique({
      where: { username },
    });

    if (!user) {
      throw new HttpException(401, 'اسم المستخدم أو كلمة المرور غير صحيحة', 'Unauthorized');
    }

    if (!user.isActive) {
      throw new HttpException(401, 'الحساب غير مفعل', 'Unauthorized');
    }

    // محاكاة التحقق من كلمة المرور
    const isPasswordValid = password === 'password123'; // للاختبار فقط
    if (!isPasswordValid) {
      throw new HttpException(401, 'اسم المستخدم أو كلمة المرور غير صحيحة', 'Unauthorized');
    }

    // إرجاع نتيجة تسجيل الدخول
    return {
      user: {
        id: user.id,
        username: user.username,
        name: user.name,
        email: user.email,
        role: user.role,
      },
      token: 'access-token-123',
      refreshToken: 'refresh-token-123',
      sessionId: 'session-123',
      isSuspicious: false,
    };
  },

  refreshToken: async (refreshToken: string, request: any) => {
    // محاكاة التحقق من refresh token
    if (refreshToken !== 'valid-refresh-token') {
      throw new HttpException(401, 'رمز التحديث غير صالح', 'Unauthorized');
    }

    // محاكاة العثور على المستخدم
    const user = await mockPrisma.user.findUnique({
      where: { id: 'user-123' },
    });

    if (!user) {
      throw new HttpException(401, 'المستخدم غير موجود', 'Unauthorized');
    }

    return {
      user: {
        id: user.id,
        username: user.username,
        name: user.name,
        email: user.email,
        role: user.role,
      },
      token: 'new-access-token',
      refreshToken: 'new-refresh-token',
      sessionId: 'session-123',
    };
  },

  logout: async (token: string, refreshToken: string, sessionId: string, userId: string) => {
    // محاكاة تسجيل الخروج
    return { success: true };
  },

  register: async (userData: any) => {
    // التحقق من عدم وجود مستخدم بنفس اسم المستخدم
    const existingUser = await mockPrisma.user.findUnique({
      where: { username: userData.username },
    });

    if (existingUser) {
      throw new HttpException(409, 'اسم المستخدم موجود بالفعل', 'Conflict');
    }

    // إنشاء المستخدم الجديد
    const newUser = await mockPrisma.user.create({
      data: {
        username: userData.username,
        password: 'hashed-password',
        name: userData.name,
        email: userData.email,
        role: userData.role || UserRole.USER,
        isActive: true,
      },
    });

    return {
      user: {
        id: newUser.id,
        username: newUser.username,
        name: newUser.name,
        email: newUser.email,
        role: newUser.role,
      },
      token: 'access-token-123',
      refreshToken: 'refresh-token-123',
      sessionId: 'session-123',
    };
  },

  changePassword: async (userId: string, currentPassword: string, newPassword: string) => {
    // العثور على المستخدم
    const user = await mockPrisma.user.findUnique({
      where: { id: userId },
    });

    if (!user) {
      throw new HttpException(404, 'المستخدم غير موجود', 'Not Found');
    }

    // التحقق من كلمة المرور الحالية
    const isCurrentPasswordValid = currentPassword === 'password123'; // للاختبار فقط
    if (!isCurrentPasswordValid) {
      throw new HttpException(401, 'كلمة المرور الحالية غير صحيحة', 'Unauthorized');
    }

    // تحديث كلمة المرور
    await mockPrisma.user.update({
      where: { id: userId },
      data: { password: 'new-hashed-password' },
    });

    return { success: true };
  },
};

const authService = mockAuthService;

describe('Auth Service', () => {
  beforeEach(() => {
    // تنظيف المحاكيات قبل كل اختبار
  });

  describe('login', () => {
    it('should login successfully with valid credentials', async () => {
      // Arrange
      const username = 'testuser';
      const password = 'password123';
      const mockUser = {
        id: 'user-123',
        username,
        name: 'Test User',
        email: '<EMAIL>',
        password: 'hashed-password',
        role: UserRole.USER,
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      prismaMock.user.findUnique.mockResolvedValue(mockUser);

      // إنشاء كائن طلب وهمي
      const mockRequest = {
        ip: '127.0.0.1',
        headers: { 'user-agent': 'Test Agent' },
        socket: { remoteAddress: '127.0.0.1' },
      } as any;

      // Act
      const result = await authService.login(username, password, mockRequest);

      // Assert
      expect(prismaMock.user.findUnique).toHaveBeenCalledWith({
        where: { username },
      });
      expect(result).toEqual({
        user: {
          id: mockUser.id,
          username: mockUser.username,
          name: mockUser.name,
          email: mockUser.email,
          role: mockUser.role,
        },
        token: 'access-token-123',
        refreshToken: 'refresh-token-123',
        sessionId: 'session-123',
        isSuspicious: false,
      });
    });

    it('should throw an error when user is not found', async () => {
      // Arrange
      const username = 'nonexistentuser';
      const password = 'password123';
      const mockRequest = {
        ip: '127.0.0.1',
        headers: { 'user-agent': 'Test Agent' },
        socket: { remoteAddress: '127.0.0.1' },
      } as any;

      prismaMock.user.findUnique.mockResolvedValue(null);

      // Act & Assert
      await expect(authService.login(username, password, mockRequest)).rejects.toEqual(
        new HttpException(401, 'اسم المستخدم أو كلمة المرور غير صحيحة', 'Unauthorized')
      );
      expect(prismaMock.user.findUnique).toHaveBeenCalledWith({
        where: { username },
      });
    });

    it('should throw an error when password is invalid', async () => {
      // Arrange
      const username = 'testuser';
      const password = 'wrongpassword';
      const mockUser = {
        id: 'user-123',
        username,
        name: 'Test User',
        email: '<EMAIL>',
        password: 'hashed-password',
        role: UserRole.USER,
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      };
      const mockRequest = {
        ip: '127.0.0.1',
        headers: { 'user-agent': 'Test Agent' },
        socket: { remoteAddress: '127.0.0.1' },
      } as any;

      prismaMock.user.findUnique.mockResolvedValue(mockUser);

      // Act & Assert
      await expect(authService.login(username, password, mockRequest)).rejects.toEqual(
        new HttpException(401, 'اسم المستخدم أو كلمة المرور غير صحيحة', 'Unauthorized')
      );
      expect(prismaMock.user.findUnique).toHaveBeenCalledWith({
        where: { username },
      });
    });

    it('should throw an error when user is inactive', async () => {
      // Arrange
      const username = 'testuser';
      const password = 'password123';
      const mockUser = {
        id: 'user-123',
        username,
        name: 'Test User',
        email: '<EMAIL>',
        password: 'hashed-password',
        role: UserRole.USER,
        isActive: false, // المستخدم غير مفعل
        createdAt: new Date(),
        updatedAt: new Date(),
      };
      const mockRequest = {
        ip: '127.0.0.1',
        headers: { 'user-agent': 'Test Agent' },
        socket: { remoteAddress: '127.0.0.1' },
      } as any;

      prismaMock.user.findUnique.mockResolvedValue(mockUser);

      // Act & Assert
      await expect(authService.login(username, password, mockRequest)).rejects.toEqual(
        new HttpException(401, 'الحساب غير مفعل', 'Unauthorized')
      );
      expect(prismaMock.user.findUnique).toHaveBeenCalledWith({
        where: { username },
      });
    });
  });

  describe('refreshToken', () => {
    it('should refresh token successfully with valid refresh token', async () => {
      // Arrange
      const refreshToken = 'valid-refresh-token';
      const mockUser = {
        id: 'user-123',
        username: 'testuser',
        name: 'Test User',
        email: '<EMAIL>',
        password: 'hashed-password',
        role: UserRole.USER,
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      };
      const mockRequest = {
        ip: '127.0.0.1',
        headers: { 'user-agent': 'Test Agent' },
        socket: { remoteAddress: '127.0.0.1' },
      } as any;

      prismaMock.user.findUnique.mockResolvedValue(mockUser);

      // Act
      const result = await authService.refreshToken(refreshToken, mockRequest);

      // Assert
      expect(prismaMock.user.findUnique).toHaveBeenCalledWith({
        where: { id: 'user-123' },
      });
      expect(result).toEqual({
        user: {
          id: mockUser.id,
          username: mockUser.username,
          name: mockUser.name,
          email: mockUser.email,
          role: mockUser.role,
        },
        token: 'new-access-token',
        refreshToken: 'new-refresh-token',
        sessionId: 'session-123',
      });
    });

    it('should throw an error when refresh token is invalid', async () => {
      // Arrange
      const refreshToken = 'invalid-refresh-token';
      const mockRequest = {
        ip: '127.0.0.1',
        headers: { 'user-agent': 'Test Agent' },
        socket: { remoteAddress: '127.0.0.1' },
      } as any;

      // Act & Assert
      await expect(authService.refreshToken(refreshToken, mockRequest)).rejects.toEqual(
        new HttpException(401, 'رمز التحديث غير صالح', 'Unauthorized')
      );
    });
  });

  describe('logout', () => {
    it('should logout successfully', async () => {
      // Arrange
      const token = 'valid-access-token';
      const refreshToken = 'valid-refresh-token';
      const sessionId = 'session-123';
      const userId = 'user-123';

      // Act
      const result = await authService.logout(token, refreshToken, sessionId, userId);

      // Assert
      expect(result).toEqual({ success: true });
    });
  });

  describe('register', () => {
    it('should register a new user successfully', async () => {
      // Arrange
      const userData = {
        username: 'newuser',
        name: 'New User',
        email: '<EMAIL>',
        role: UserRole.USER,
      };

      prismaMock.user.findUnique.mockResolvedValue(null); // لا يوجد مستخدم بنفس الاسم
      prismaMock.user.create.mockResolvedValue({
        id: 'new-user-123',
        username: userData.username,
        password: 'hashed-password',
        name: userData.name,
        email: userData.email,
        role: userData.role,
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      });

      // Act
      const result = await authService.register(userData);

      // Assert
      expect(prismaMock.user.findUnique).toHaveBeenCalledWith({
        where: { username: userData.username },
      });
      expect(prismaMock.user.create).toHaveBeenCalledWith({
        data: {
          username: userData.username,
          password: 'hashed-password',
          name: userData.name,
          email: userData.email,
          role: userData.role,
          isActive: true,
        },
      });
      expect(result).toEqual({
        user: {
          id: 'new-user-123',
          username: userData.username,
          name: userData.name,
          email: userData.email,
          role: userData.role,
        },
        token: 'access-token-123',
        refreshToken: 'refresh-token-123',
        sessionId: 'session-123',
      });
    });

    it('should throw an error when username already exists', async () => {
      // Arrange
      const userData = {
        username: 'existinguser',
        name: 'Existing User',
        email: '<EMAIL>',
      };

      prismaMock.user.findUnique.mockResolvedValue({
        id: 'existing-user-123',
        username: userData.username,
        name: userData.name,
        email: userData.email,
        password: 'hashed-password',
        role: UserRole.USER,
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      });

      // Act & Assert
      await expect(authService.register(userData)).rejects.toEqual(
        new HttpException(409, 'اسم المستخدم موجود بالفعل', 'Conflict')
      );
      expect(prismaMock.user.findUnique).toHaveBeenCalledWith({
        where: { username: userData.username },
      });
      expect(prismaMock.user.create).not.toHaveBeenCalled();
    });
  });

  describe('changePassword', () => {
    it('should change password successfully', async () => {
      // Arrange
      const userId = 'user-123';
      const currentPassword = 'password123';
      const newPassword = 'newpassword456';
      const mockUser = {
        id: userId,
        username: 'testuser',
        name: 'Test User',
        email: '<EMAIL>',
        password: 'hashed-password',
        role: UserRole.USER,
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      prismaMock.user.findUnique.mockResolvedValue(mockUser);
      prismaMock.user.update.mockResolvedValue({
        ...mockUser,
        password: 'new-hashed-password',
      });

      // Act
      const result = await authService.changePassword(userId, currentPassword, newPassword);

      // Assert
      expect(prismaMock.user.findUnique).toHaveBeenCalledWith({
        where: { id: userId },
      });
      expect(prismaMock.user.update).toHaveBeenCalledWith({
        where: { id: userId },
        data: { password: 'new-hashed-password' },
      });
      expect(result).toEqual({ success: true });
    });

    it('should throw an error when user is not found', async () => {
      // Arrange
      const userId = 'nonexistent-user';
      const currentPassword = 'password123';
      const newPassword = 'newpassword456';

      prismaMock.user.findUnique.mockResolvedValue(null);

      // Act & Assert
      await expect(authService.changePassword(userId, currentPassword, newPassword)).rejects.toEqual(
        new HttpException(404, 'المستخدم غير موجود', 'Not Found')
      );
      expect(prismaMock.user.findUnique).toHaveBeenCalledWith({
        where: { id: userId },
      });
      expect(prismaMock.user.update).not.toHaveBeenCalled();
    });

    it('should throw an error when current password is invalid', async () => {
      // Arrange
      const userId = 'user-123';
      const currentPassword = 'wrongpassword';
      const newPassword = 'newpassword456';
      const mockUser = {
        id: userId,
        username: 'testuser',
        name: 'Test User',
        email: '<EMAIL>',
        password: 'hashed-password',
        role: UserRole.USER,
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      prismaMock.user.findUnique.mockResolvedValue(mockUser);

      // Act & Assert
      await expect(authService.changePassword(userId, currentPassword, newPassword)).rejects.toEqual(
        new HttpException(401, 'كلمة المرور الحالية غير صحيحة', 'Unauthorized')
      );
      expect(prismaMock.user.findUnique).toHaveBeenCalledWith({
        where: { id: userId },
      });
      expect(prismaMock.user.update).not.toHaveBeenCalled();
    });
  });
});
