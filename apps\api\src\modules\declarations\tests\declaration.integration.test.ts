import request from 'supertest';
import path from 'path';
import fs from 'fs';
import {
  app,
  setupTestFolders,
  cleanupDatabase,
  setupTestData,
  cleanupFiles,
} from '../../../core/utils/test/integration-setup.js';
import { config } from '../../../core/config/app.config.js';
import { prisma } from '../../../core/utils/prisma.js';

/**
 * اختبارات تكامل وحدة البيانات
 */
describe('Declarations Integration Tests', () => {
  let authToken: string;
  let userId: string;
  let clientId: string;
  let createdFiles: string[] = [];
  let declarationId: string;

  // قبل جميع الاختبارات
  beforeAll(async () => {
    // إعداد بيئة الاختبار
    setupTestFolders();
    await cleanupDatabase();

    // إنشاء بيانات الاختبار
    const { user, authToken: token, client } = await setupTestData();
    userId = user.id;
    authToken = token;
    clientId = client.id;
  });

  // بعد جميع الاختبارات
  afterAll(async () => {
    // تنظيف الملفات المؤقتة
    cleanupFiles(createdFiles);
  });

  /**
   * اختبار إنشاء بيان جديد
   */
  describe('POST /api/declarations', () => {
    it('يجب أن ينشئ بيان جديد بنجاح', async () => {
      // إنشاء ملف PDF وهمي للاختبار
      const testPdfPath = path.join(config.upload.dir, 'test-declaration.pdf');
      fs.writeFileSync(testPdfPath, 'PDF test content');
      createdFiles.push(testPdfPath);

      const response = await request(app)
        .post('/api/declarations')
        .set('Authorization', `Bearer ${authToken}`)
        .field('declarationNumber', '2001')
        .field('taxNumber', '*********')
        .field('clientName', 'عميل اختبار')
        .field('companyName', 'شركة اختبار')
        .field('policyNumber', '6001')
        .field('invoiceNumber', '11001')
        .field('gatewayEntryNumber', '3001')
        .field('declarationType', 'IMPORT')
        .field('declarationDate', new Date().toISOString())
        .field('count', '50')
        .field('weight', '200 KG')
        .field('goodsType', 'MEDICAL_DEVICES')
        .field('itemsCount', '3')
        .field('entryDate', new Date().toISOString())
        .field('exitDate', new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString())
        .field('clientId', clientId)
        .attach('pdfFile', testPdfPath)
        .expect(201);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toBeDefined();
      expect(response.body.data.declarationNumber).toBe(2001);
      expect(response.body.data.taxNumber).toBe('*********');
      expect(response.body.data.clientName).toBe('عميل اختبار');
      expect(response.body.data.pdfFile).toBeDefined();

      // حفظ معرف البيان للاختبارات اللاحقة
      declarationId = response.body.data.id;

      // إضافة ملف PDF المنشأ إلى قائمة الملفات للتنظيف
      if (response.body.data.pdfFile) {
        createdFiles.push(path.join(config.upload.dir, response.body.data.pdfFile));
      }
    });

    it('يجب أن يرفض إنشاء بيان بنفس رقم البيان', async () => {
      const response = await request(app)
        .post('/api/declarations')
        .set('Authorization', `Bearer ${authToken}`)
        .field('declarationNumber', '2001') // نفس رقم البيان السابق
        .field('taxNumber', '*********')
        .field('clientName', 'عميل اختبار')
        .field('companyName', 'شركة اختبار')
        .field('policyNumber', '6001')
        .field('invoiceNumber', '11001')
        .field('gatewayEntryNumber', '3001')
        .field('declarationType', 'IMPORT')
        .field('declarationDate', new Date().toISOString())
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toBeDefined();
    });
  });

  /**
   * اختبار الحصول على البيانات
   */
  describe('GET /api/declarations', () => {
    it('يجب أن يعيد قائمة البيانات بنجاح', async () => {
      const response = await request(app)
        .get('/api/declarations')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toBeDefined();
      expect(Array.isArray(response.body.data)).toBe(true);
      expect(response.body.data.length).toBeGreaterThan(0);
      expect(response.body.pagination).toBeDefined();
    });

    it('يجب أن يعيد البيانات مع تصفية حسب نوع البيان', async () => {
      const response = await request(app)
        .get('/api/declarations')
        .set('Authorization', `Bearer ${authToken}`)
        .query({ declarationType: 'IMPORT' })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toBeDefined();
      expect(Array.isArray(response.body.data)).toBe(true);
      expect(response.body.data.length).toBeGreaterThan(0);
      expect(response.body.data[0].declarationType).toBe('IMPORT');
    });
  });

  /**
   * اختبار الحصول على بيان محدد
   */
  describe('GET /api/declarations/:id', () => {
    it('يجب أن يعيد بيان محدد بنجاح', async () => {
      const response = await request(app)
        .get(`/api/declarations/${declarationId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toBeDefined();
      expect(response.body.data.id).toBe(declarationId);
      expect(response.body.data.declarationNumber).toBe(2001);
    });

    it('يجب أن يعيد خطأ عند طلب بيان غير موجود', async () => {
      const response = await request(app)
        .get('/api/declarations/non-existent-id')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(404);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toBeDefined();
    });
  });

  /**
   * اختبار تحديث بيان
   */
  describe('PUT /api/declarations/:id', () => {
    it('يجب أن يحدث بيان بنجاح', async () => {
      const response = await request(app)
        .put(`/api/declarations/${declarationId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .field('declarationNumber', '2001')
        .field('taxNumber', '*********')
        .field('clientName', 'عميل اختبار محدث')
        .field('companyName', 'شركة اختبار محدثة')
        .field('goodsType', 'MEDICAL_SUPPLIES')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toBeDefined();
      expect(response.body.data.id).toBe(declarationId);
      expect(response.body.data.clientName).toBe('عميل اختبار محدث');
      expect(response.body.data.companyName).toBe('شركة اختبار محدثة');
      expect(response.body.data.goodsType).toBe('MEDICAL_SUPPLIES');
    });
  });

  /**
   * اختبار حذف بيان
   */
  describe('DELETE /api/declarations/:id', () => {
    it('يجب أن يحذف بيان بنجاح', async () => {
      const response = await request(app)
        .delete(`/api/declarations/${declarationId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toBeDefined();
      expect(response.body.data.id).toBe(declarationId);

      // التحقق من حذف البيان من قاعدة البيانات
      const deletedDeclaration = await prisma.declaration.findUnique({
        where: { id: declarationId },
      });
      expect(deletedDeclaration).toBeNull();
    });
  });
});
